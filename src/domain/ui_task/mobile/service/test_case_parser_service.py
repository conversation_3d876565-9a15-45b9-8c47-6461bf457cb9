#!/usr/bin/env python3
"""
测试用例解析节点

负责解析测试用例描述，提取步骤信息
支持聚合模式下使用GPT-4模型智能拆分步骤
"""

import json
from typing import List

from langchain_core.prompts import ChatPromptTemplate
from loguru import logger

from src.domain.ui_task.mobile.repo.do.State import DeploymentState
from src.infra.model import get_chat_model


def parse_test_case_node(state: DeploymentState) -> DeploymentState:
    """
    Parse test case description into individual steps
    支持分步模式和聚合模式的步骤解析
    """
    task_id = state["task_id"]
    logger.info(f"[{task_id}] 📋 Parsing test case into steps...")

    # Get test case description
    test_description = state.get("test_case_description", "")
    if not test_description:
        test_description = state.get("task", "")

    # 检查验证模式
    verification_mode = state.get("verification_mode", "aggregation")
    logger.info(f"[{task_id}] 🔍 Parsing mode: {verification_mode}")
    logger.info(f"[{task_id}] 🔍 Parsing test case steps from: {test_description}")

    # 根据验证模式选择不同的解析策略
    if verification_mode == "step_by_step":
        # 分步模式：任务已经被拆分为步骤，直接使用预定义的步骤列表
        steps = state.get("task_steps", [])
        if not steps:
            logger.warning(f"[{task_id}] ⚠️ Step-by-step mode but no predefined steps, using description as single step")
            steps = [test_description.strip()] if test_description.strip() else []
        logger.info(f"[{task_id}] ✓ Step-by-step mode using {len(steps)} predefined steps")
    else:
        # 聚合模式：使用GPT-4智能拆分步骤
        steps = parse_aggregation_steps_with_gpt4(test_description, task_id)

    if not steps:
        logger.error(f"[{task_id}] ❌ No steps found in test case description")
        state["completed"] = True
        state["execution_status"] = "failed"
        return state

    logger.info(f"[{task_id}] ✓ Parsed {len(steps)} steps: {steps}")

    # Store parsed information
    state["task_steps"] = steps
    state["step_failed"] = False
    state["retry_count"] = 0
    state["max_retries"] = len(steps) * 2  # 根据步骤数量设置最大重试次数

    logger.info(f"[{task_id}] ✓ Parsed {len(steps)} steps for execution, max_retries set to {state['max_retries']}")

    return state


def parse_aggregation_steps_with_gpt4(description: str, task_id: str) -> List[str]:
    """
    使用GPT-4模型智能拆分聚合模式的任务描述为独立步骤

    Args:
        description: 任务描述文本
        task_id: 任务ID用于日志

    Returns:
        步骤描述列表
    """
    logger.info(f"[{task_id}] 🤖 Using GPT-4 to intelligently parse aggregation steps...")

    try:
        # 直接使用GPT-4进行智能拆分，不再依赖规则解析
        model = get_chat_model()

        # 构建提示模板
        prompt_template = ChatPromptTemplate.from_messages([
            ("system", """你是一个专业的UI自动化测试专家。你的任务将用户提供的UI任务描述按照已经设置好的序号拆分出来。

请遵循以下原则：
用户输入的UI任务描述格式均为 1.xxx 2.xxx 3.xxx，就按照标号拆分，不要按照其他方式拆分，如果没有标号，就默认一个步骤。

请以JSON格式返回结果：
{{
    "steps": [
        "步骤1的描述",
        "步骤2的描述",
        "步骤3的描述"
    ],
    "reasoning": "拆分的理由和思考过程"
}}"""),
            ("human", "请将以下UI任务描述拆分为独立的执行步骤：\n\n{task_description}")
        ])

        # 调用GPT-4
        chain = prompt_template | model
        response = chain.invoke({"task_description": description})

        # 解析响应
        try:
            # 尝试解析JSON响应
            response_text = response.content.strip()

            # 如果响应被包装在代码块中，提取JSON部分
            if "```json" in response_text:
                json_start = response_text.find("```json") + 7
                json_end = response_text.find("```", json_start)
                response_text = response_text[json_start:json_end].strip()
            elif "```" in response_text:
                json_start = response_text.find("```") + 3
                json_end = response_text.rfind("```")
                response_text = response_text[json_start:json_end].strip()

            parsed_response = json.loads(response_text)
            steps = parsed_response.get("steps", [])
            reasoning = parsed_response.get("reasoning", "")

            if steps:
                logger.info(f"[{task_id}] 🧠 GPT-4 reasoning: {reasoning}")
                logger.info(f"[{task_id}] ✓ GPT-4 successfully parsed {len(steps)} steps")
                return steps
            else:
                logger.warning(f"[{task_id}] ⚠️ GPT-4 returned empty steps")

        except json.JSONDecodeError as e:
            logger.error(f"[{task_id}] ❌ Failed to parse GPT-4 JSON response: {str(e)}")
            logger.error(f"[{task_id}] Raw response: {response.content}")

        # 如果GPT-4解析失败，回退到简单拆分
        logger.warning(f"[{task_id}] ⚠️ GPT-4 parsing failed, falling back to single step")
        return [description.strip()]  # 至少返回原始描述作为单个步骤

    except Exception as e:
        logger.error(f"[{task_id}] ❌ Error in GPT-4 step parsing: {str(e)}")
        # 出错时回退到原始描述
        return [description.strip()]