#!/usr/bin/env python3
"""
任务停止管理器

负责管理基于taskid的任务停止状态
"""

import threading
from typing import Dict, Set
from loguru import logger
from datetime import datetime
from src.schema.action_types import ExecutionStatus


class TaskStopManager:
    """任务停止管理器 - 单例模式"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(TaskStopManager, cls).__new__(cls)
                    cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        if not self._initialized:
            self._stopped_tasks: Set[str] = set()
            self._task_info: Dict[str, Dict] = {}
            self._lock = threading.Lock()
            self._initialized = True
    
    def stop_task(self, task_id: str) -> bool:
        """
        停止指定的任务

        Args:
            task_id: 任务ID

        Returns:
            是否成功标记为停止
        """
        with self._lock:
            if task_id in self._task_info:
                self._stopped_tasks.add(task_id)
                self._task_info[task_id]["stop_time"] = datetime.now().isoformat()
                self._task_info[task_id]["status"] = "stopped"
                logger.info(f"[{task_id}] 🛑 Task {task_id} marked for stop")

                # 同步更新数据库状态
                try:
                    from src.domain.ui_task.mobile.service.task_persistence_service import task_persistence_service
                    task_persistence_service.update_task_status(
                        task_id=task_id,
                        status=ExecutionStatus.PAUSED,
                        error_message="用户停止"
                    )
                except Exception as e:
                    logger.warning(f"[{task_id}] ⚠️ Failed to update database status for stopped task {task_id}: {str(e)}")

                return True
            else:
                logger.warning(f"[{task_id}] ⚠️ Task {task_id} not found in active tasks")
                return False
    
    def is_task_stopped(self, task_id: str) -> bool:
        """
        检查任务是否被停止
        
        Args:
            task_id: 任务ID
            
        Returns:
            任务是否被停止
        """
        with self._lock:
            return task_id in self._stopped_tasks
    
    def register_task(self, task_id: str, task_name: str = "") -> None:
        """
        注册新任务
        
        Args:
            task_id: 任务ID
            task_name: 任务名称
        """
        with self._lock:
            self._task_info[task_id] = {
                "task_name": task_name,
                "start_time": datetime.now().isoformat(),
                "status": "running"
            }
            logger.info(f"[{task_id}] 📝 Task {task_id} registered: {task_name}")
    
    def unregister_task(self, task_id: str) -> None:
        """
        注销任务（任务完成或失败时调用）
        
        Args:
            task_id: 任务ID
        """
        with self._lock:
            self._stopped_tasks.discard(task_id)
            if task_id in self._task_info:
                del self._task_info[task_id]
                logger.info(f"[{task_id}] 🗑️ Task {task_id} unregistered")
    
    def get_task_status(self, task_id: str) -> Dict:
        """
        获取任务状态信息
        
        Args:
            task_id: 任务ID
            
        Returns:
            任务状态信息
        """
        with self._lock:
            if task_id in self._task_info:
                return {
                    "task_id": task_id,
                    "is_stopped": task_id in self._stopped_tasks,
                    **self._task_info[task_id]
                }
            else:
                return {
                    "task_id": task_id,
                    "is_stopped": False,
                    "status": "not_found"
                }
    
    def get_all_tasks(self) -> Dict[str, Dict]:
        """
        获取所有任务状态
        
        Returns:
            所有任务的状态信息
        """
        with self._lock:
            result = {}
            for task_id, info in self._task_info.items():
                result[task_id] = {
                    "task_id": task_id,
                    "is_stopped": task_id in self._stopped_tasks,
                    **info
                }
            return result


# 全局单例实例
task_stop_manager = TaskStopManager()
