#!/usr/bin/env python3
"""
预期结果验证Agent

负责验证步骤执行结果是否符合预期，当决策agent推进步骤时触发验证
"""

import json
import time
from datetime import datetime
from typing import Dict, Any, Optional, Tuple

from langchain_core.prompts import ChatPromptTemplate
from loguru import logger

from src.domain.ui_task.mobile.aggregate.prompt.action_verification_prompt import get_action_verification_prompt
from src.domain.ui_task.mobile.aggregate.prompt.step_verification_prompt import get_step_verification_prompt, \
    get_user_step_verification_invoke_prompt
from src.domain.ui_task.mobile.android.image_processor import ImageProcessor
from src.domain.ui_task.mobile.android.screenshot_manager import convert_screenshot_to_base64, take_screenshot
from src.domain.ui_task.mobile.repo.do.State import DeploymentState
from src.infra.model import get_chat_model


class ExpectationVerificationAgent:
    """预期结果验证Agent"""

    def __init__(self):
        self.model = get_chat_model()

    @staticmethod
    def _escape_template_variables(text: str) -> str:
        """
        转义文本中的模板变量符号，防止LangChain将其识别为变量

        Args:
            text: 原始文本

        Returns:
            转义后的文本
        """
        if not text:
            return text

        # 将单个花括号转义为双花括号
        # 这样LangChain就不会将其识别为模板变量
        return text.replace("{", "{{").replace("}", "}}")

    def verify_step_expectation(
            self,
            state: DeploymentState,
            previous_step_index: int,
            expected_text: Optional[str] = None,
            expected_image: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        验证上一步骤是否符合预期结果

        Args:
            state: 当前状态
            previous_step_index: 上一步骤索引
            expected_text: 期望的文字描述
            expected_image: 期望的图片（base64）

        Returns:
            验证结果字典
        """
        try:

            screenshot_path = take_screenshot(
              device=state["device"],
              task_id=state["task_id"],
              action_name="verify_step_expectation"
            )

            current_screenshot_base64 = convert_screenshot_to_base64(screenshot_path, state["task_id"])

            logger.info(f"[{state['task_id']}] 🔍 Verifying step {previous_step_index + 1} expectation...")

            # 获取步骤信息
            task_steps = state.get("task_steps", [])
            if previous_step_index >= len(task_steps):
                return {
                    "verified": False,
                    "reason": "Step index out of range",
                    "timestamp": datetime.now().isoformat()
                }

            previous_step_name = task_steps[previous_step_index]

            # 检查是否已经验证过这个步骤，避免重复验证
            step_verification_results = state.get("step_verification_results", [])
            if (previous_step_index < len(step_verification_results) and
                    step_verification_results[previous_step_index] and
                    step_verification_results[previous_step_index].get("verified") is not None):
                existing_result = step_verification_results[previous_step_index]
                logger.info(
                    f"[{state['task_id']}] ℹ️ Step {previous_step_index + 1} already verified: {existing_result.get('verified')}")
                return existing_result

            # 处理期望图片：将文件路径转换为base64
            processed_expected_image = None
            if expected_image:
                processed_expected_image = ImageProcessor.convert_image_path_to_base64(expected_image)
                if processed_expected_image is None:
                    logger.warning(
                        f"[{state['task_id']}] ⚠️ Failed to convert expected image to base64: {expected_image}")
                else:
                    logger.info(f"[{state['task_id']}] ✅ Successfully converted expected image to base64")
            else:
                logger.info(f"[{state['task_id']}] ℹ️ No expected image provided for verification")

            # 构建验证提示
            verification_prompt = get_step_verification_prompt(
                state=state,
                previous_step_name=previous_step_name,
                expected_text=expected_text,
                expected_image=processed_expected_image,
            )
            print(verification_prompt)
            messages = [{
                "role": "system",
                "content": verification_prompt
            },
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": "########## 当前界面 ##########"
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/png;base64,{current_screenshot_base64}"
                            }
                        }
                    ]
                }
            ]

            if processed_expected_image:
                messages.append(
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": "########## 期望图片 ##########"
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/png;base64,{processed_expected_image}"
                                }
                            }
                        ]
                    }
                )

            messages.append({
                "role": "user",
                "content": get_user_step_verification_invoke_prompt()
            })

            # 调用模型
            start_time = time.time()
            prompt = ChatPromptTemplate.from_messages(messages=messages)
            chain = prompt | self.model
            output = chain.invoke({})
            model_response = output.content
            end_time = time.time()

            logger.info(f"[{state['task_id']}] 验证耗时: {end_time - start_time:.2f}s")
            logger.info(f"[{state['task_id']}] 验证响应: {model_response}")

            # 解析JSON结果
            parsed_result = self._parse_verification_result(model_response, state)

            verification_result = {
                "verified": parsed_result["verified"],
                "reason": parsed_result["reason"],
                "timestamp": datetime.now().isoformat()
            }

            # 更新验证结果到状态中，避免重复验证
            step_verification_results = state.get("step_verification_results", [])
            while len(step_verification_results) <= previous_step_index:
                step_verification_results.append({})
            step_verification_results[previous_step_index] = verification_result
            state["step_verification_results"] = step_verification_results

            logger.info(
                f"[{state['task_id']}] ✅ Step {previous_step_index + 1} verification result saved: {verification_result['verified']}")

            return verification_result

        except Exception as e:
            logger.error(f"[{state['task_id']}] ❌ Error in expectation verification: {str(e)}")
            return {
                "verified": False,
                "reason": f"Verification error: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }

    def _get_previous_action_decision(self, state: DeploymentState, current_decision_fields: Dict[str, Any]) -> str:
        """
        获取上一轮的action_decision用于验证

        Args:
            state: 当前状态
            current_decision_fields: 当前轮决策字段（作为备选）

        Returns:
            上一轮的action_decision，如果找不到则返回当前轮的
        """
        try:
            # 从历史记录中查找最近的执行记录
            history = state.get("history", [])
            execution_records = [r for r in history if
                                 r.get("action") == "enhanced_get_location" and
                                 r.get("decision_fields")]

            if execution_records:
                # 获取最近一轮的执行记录
                latest_record = execution_records[-1]
                previous_decision_fields = latest_record.get("decision_fields", {})
                previous_action_decision = previous_decision_fields.get("action_decision", "")

                if previous_action_decision:
                    # 截取前100个字符用于日志显示
                    action_preview = previous_action_decision[:100] + "..." if len(
                        previous_action_decision) > 100 else previous_action_decision
                    logger.info(
                        f"[{state['task_id']}] 📋 Using previous action_decision for verification: {action_preview}")
                    return previous_action_decision
                else:
                    logger.warning(
                        f"[{state['task_id']}] ⚠️ Previous action_decision is empty, using current as fallback")
            else:
                logger.warning(
                    f"[{state['task_id']}] ⚠️ No previous execution records found, using current action_decision as fallback")

        except Exception as e:
            logger.error(f"[{state['task_id']}] ❌ Error getting previous action_decision: {str(e)}")

        # 如果无法获取上一轮的，则使用当前轮的作为备选
        current_action_decision = current_decision_fields.get("action_decision", "")
        if current_action_decision:
            action_preview = current_action_decision[:100] + "..." if len(
                current_action_decision) > 100 else current_action_decision
            logger.info(f"[{state['task_id']}] 📋 Using current action_decision as fallback: {action_preview}")
        else:
            logger.warning(f"[{state['task_id']}] ⚠️ Both previous and current action_decision are empty")

        return current_action_decision

    def _parse_verification_result(self, model_response: str, state: DeploymentState) -> Dict[str, Any]:
        """
        解析验证结果JSON

        Args:
            model_response: 模型响应文本
            state: 当前状态

        Returns:
            包含verified和reason的字典
        """
        try:
            import re

            # 提取JSON部分
            json_match = re.search(r'\{.*}', model_response, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                result = json.loads(json_str)

                # 返回verified和reason
                if "verified" in result:
                    return {
                        "verified": result["verified"],
                        "reason": result.get("reason", "")
                    }

            # 如果没有找到JSON，默认返回失败
            return {
                "verified": False,
                "reason": "无法解析验证响应"
            }

        except Exception as e:
            logger.error(f"[{state['task_id']}] ❌ 验证结果JSON解析失败: {str(e)}")
            return {
                "verified": False,
                "reason": f"JSON解析错误: {str(e)}"
            }

    def verify_action_execution(
            self,
            state: DeploymentState,
            decision_fields: Dict[str, Any],
            action_command: str,
            image_data_base64: str,
            before_screenshot_path: str,
            coordinates: Optional[Tuple[int, int]] = None
    ) -> Dict[str, Any]:
        """
        验证动作执行是否正确

        在动作执行后调用此方法，会：
        1. 等待3秒
        2. 截取执行后的界面
        3. 在执行前图片上标注操作位置
        4. 使用大模型判断：
           - 准星标记的位置元素与决策内容描述的是否一致
           - 执行后的截图是否符合预期

        Args:
            state: 当前状态
            decision_fields: 决策agent的输出字段
            action_command: 执行的动作命令
            image_data_base64: str
            before_screenshot_path: 执行前的截图路径
            coordinates: 操作坐标（可选，如果提供则用于标注）

        Returns:
            验证结果字典
        """
        try:
            task_id = state["task_id"]
            logger.info(f"[{task_id}] 🔍 Starting action execution verification...")

            # 1. 等待3秒，让界面稳定
            logger.info(f"[{task_id}] ⏳ Waiting 3 seconds for UI to stabilize...")
            time.sleep(3)

            # 4. 转换图片为base64验证规则
            before_image_base64 = convert_screenshot_to_base64(before_screenshot_path, task_id)

            if not before_image_base64:
                return {
                    "verified": False,
                    "reason": "Failed to convert screenshots to base64",
                    "timestamp": datetime.now().isoformat()
                }

            # 5. 构建验证提示并调用大模型
            verification_prompt = get_action_verification_prompt(
                decision_fields=decision_fields
            )

            messages = [
                {
                    "role": "system",
                    "content": verification_prompt
                },
                {
                    "role": "system",
                    "content": """
########## 执行验证 ##########
严格遵守<验证规则>, 按照<验证方式>进行验证，严格按照 <输出要求> 输出验证结果                  
                    """
                },
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": "########## 标记前截图 ##########"
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/png;base64,{image_data_base64}"
                            }
                        }
                    ]
                },
                {
                    "role": "user",
                    "content": [

                        {
                            "type": "text",
                            "text": "########## 标记后截图 ##########"
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/png;base64,{before_image_base64}"
                            }
                        }
                    ]
                }
            ]

            # 调用模型
            start_time = time.time()
            prompt = ChatPromptTemplate.from_messages(messages=messages)
            chain = prompt | self.model
            output = chain.invoke({})
            model_response = output.content
            end_time = time.time()

            logger.info(f"[{task_id}] 动作执行验证耗时: {end_time - start_time:.2f}s")
            logger.info(f"[{task_id}] 动作执行验证响应: {model_response}")

            # 解析验证结果
            parsed_result = self._parse_verification_result(model_response, state)

            verification_result = {
                "verified": parsed_result["verified"],
                "reason": parsed_result["reason"],
                "timestamp": datetime.now().isoformat(),
                "before_screenshot_path": before_screenshot_path,
                "after_screenshot_path": before_screenshot_path,
                "action_command": action_command,
                "coordinates": coordinates
            }

            logger.info(f"[{task_id}] ✅ Action execution verification completed: {verification_result['verified']}")
            return verification_result

        except Exception as e:
            logger.error(f"[{state.get('task_id', 'unknown')}] ❌ Error in action execution verification: {str(e)}")
            return {
                "verified": False,
                "reason": f"Verification error: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }


expectation_verification_agent = ExpectationVerificationAgent()
