from typing import Optional

from src.domain.ui_task.mobile.repo.do.State import DeploymentState


def get_step_verification_prompt(
        state: DeploymentState,
        previous_step_name: str,
        expected_text: Optional[str],
        expected_image: Optional[str],
) -> str:
    """
    获取步骤验证提示
    
    Args:
        state: 当前状态
        previous_step_name: 上一步骤名称
        expected_text: 期望文字
        expected_image: 期望图片（base64）

    Returns:
        验证提示文本
    """
    # 获取测试用例信息并转义
    test_case_name = _escape_template_variables(state.get("test_case_name", ""))
    escaped_previous_step_name = _escape_template_variables(previous_step_name)
    escaped_expected_text = _escape_template_variables(expected_text or "")

    prompt = f"""
########## 角色 ##########
{get_role_definition()}

########## 验证背景 ##########
{get_verification_background(test_case_name, escaped_previous_step_name)}
{get_expected_result_description(escaped_expected_text, expected_image)}

########## 验证流程 ##########
{get_step_verification_invoke_prompt()}

########### 输出格式 ##########
{get_output_format()}

########### 输出要求 ##########
{get_output_requirements()}
"""
    return prompt


def get_user_step_verification_invoke_prompt() -> str :
    return """
    ####################################
    1.请严格按照<验证流程>执行
    2.请严格按照<输出要求>输出结果
    ####################################
    """

def get_step_verification_invoke_prompt() -> str:
    return f"""
1. 读取<验证背景>中的'期望结果'，带着期望结果去观察<当前界面>
2. 对比<当前界面>与'期望结果'，按照以下要求判断：
   - 期望文字：<当前界面>与期望文字描述的关键信息高度一致
   - 期望图片：<当前界面>内容应与<期望图片>描述内容高度一致
3. 输出JSON格式的验证结果，严格按照<输出格式>输出
"""


def get_expected_result_description(escaped_expected_text, expected_image):
    # 构建期望图片说明
    has_expected_image = False
    if expected_image:
        has_expected_image = True

    expected_result_description = ""

    if escaped_expected_text and has_expected_image:
        expected_result_description = f"""
**当前步骤期望结果包含文字内容与图片内容**
**期望结果** 
- 文字内容：{escaped_expected_text}         
- 图片内容：请查看<期望图片>
"""
    elif escaped_expected_text and not has_expected_image:
        expected_result_description = f"""
**当前步骤期望结果仅包含文字内容**
**期望结果** 
- 文字内容：{escaped_expected_text}         
"""
    elif not escaped_expected_text and has_expected_image:
        expected_result_description = f"""
**当前步骤期望结果仅包含图片内容**
**期望结果** 
- 图片内容：请查看<期望图片>
"""
    return expected_result_description


def get_role_definition() -> str:
    """获取角色定义"""
    return "你是一个UI自动化测试监督Agent，专门负责校验决策Agent的执行结果是否符合预期。"


def get_verification_background(test_case_name: str, previous_step_name: str) -> str:
    """获取验证背景"""
    return f"""
**执行步骤**：{previous_step_name}
"""


def get_output_format() -> str:
    """获取输出格式要求"""
    return """
{{
  "verified": 使用false/true表示验证是否通过, 
  "reason": "描述<当前界面>与<期望结果>的差异情况"
}}
"""

def get_output_requirements():
    return """
严格按照JSON格式输出结果，严格按照<输出格式>输出  
    """


def _escape_template_variables(text: str) -> str:
    """
    转义文本中的模板变量符号，防止LangChain将其识别为变量
    
    Args:
        text: 原始文本
        
    Returns:
        转义后的文本
    """
    if not text:
        return text

    # 将单个花括号转义为双花括号
    # 这样LangChain就不会将其识别为模板变量
    return text.replace("{", "{{").replace("}", "}}")
