from typing import Dict, List

from src.domain.ui_task.mobile.repo.do.State import DeploymentState


def get_decision_definition_prompt(state: DeploymentState, execution_records: List[Dict],
                                   has_execution_history: bool) -> str:
    current_execution_count = state.get("execution_count", 0)

    system_instruction = f'''
########## 角色定位 ##########
{get_role_definition(state)}

########## 测试用例信息 ##########
{get_test_case_description(state)}

########## 动作列表 ##########
{get_action_list()}

########## UI组件操作说明 ##########
{get_ui_component_instructions(state)}

########## 特殊场景 ##########
{get_special_scenarios(state)}

########## 执行记忆 ##########
{get_execute_history(execution_records)}

########## 界面截图说明 ##########
{get_screenshot_description(current_execution_count, has_execution_history)}

########## 准星标记 ##########
{get_red_mark_prompt()}

########## 图片元素提取 ##########
{get_image_element_extractor_prompt()}

########## 界面分析 ##########
{get_interface_analysis_prompt()}

########## 步骤预期结果验证 ##########
{get_step_verification_failure_context(state)}

########## 回滚动作 ##########
{get_rollback_guidance()}

########## 自检流程 ##########
{get_self_check_prompt()}

########## 执行步骤流程 ##########
{get_execution_step_rule_prompt()}

########## 动作决策 ##########
{get_action_decision_prompt()}

########## 输出格式 ##########
{get_output_example()}

########## 输出要求 ##########
{get_output_requirement()}'''
    print(system_instruction)
    return system_instruction


# ########### 输出字段说明 ##########
# {get_keyword_decision()}

def get_role_definition(state: DeploymentState) -> str:
    """获取角色定义"""
    return f"""你是一个专业的测试用例执行决策Agent，专门负责安卓软件的UI自动化测试决策。
**核心职责**：
1. **分析测试场景**：基于截图和测试用例要求，分析当前界面状态和执行进度
2. **制定执行决策**：为当前步骤制定精确的执行策略和具体动作指令

**合作伙伴**：
1. **执行Agent**：它会根据你输出的决策和动作指令执行具体的UI操作，过程中可能会产生操作错误，你要考虑出错情况，及时纠正
2. **监督Agent**：它会监督你的决策和执行结果，如果步骤执行失败，它会在<执行记忆>中拼接验证错误信息，你要根据错误描述重新进行决策
"""


def get_app_name(state: DeploymentState) -> str:
    """获取应用名称"""
    return state.get("app_name") or "TT语音"


def get_app_description(state: DeploymentState) -> str:
    """获取应用功能介绍"""
    custom_description = state.get("app_description")
    if custom_description:
        return custom_description
    return """1.实时语音开黑
  - 支持多人语音房间，适用于王者荣耀、和平精英、英雄联盟等热门游戏；
  - 自动匹配队友，开黑时自动连麦。
2.社交房间/语音聊天室
  - 用户可以创建语音房间，进行聊天、K歌、相亲、交友等活动；
  - 提供"电台"、"陪玩"、"唱歌"等丰富的语音互动玩法。
3.语音变声器与特效
  - 多种语音变声（如萝莉、大叔、机器人等）；
  - 增加趣味性和个性化体验。
4.公会系统与赛事组织
  - 支持游戏公会管理，成员可在TT语音中组织赛事或日常语音活动；
  - 适用于公会团队协作、管理、通知等。
5.陪玩/找队友/开黑匹配
  - 用户可发布自己擅长的游戏技能，或找人一起组队打游戏；
  - 一些服务有付费机制（陪玩）。"""


def get_test_case_description(state: DeploymentState) -> str:
    test_case_name = state.get("test_case_name", "未知测试用例")
    test_case_description = state.get("test_case_description", state.get("task", ""))
    expected_result = state.get("expected_result", "")
    return f"""- 用例名称: {test_case_name}
- 用例步骤:
{test_case_description}
- 期望结果: {expected_result}"""


def get_action_list() -> str:
    """获取动作列表"""
    return """1.click
  作用：模拟用户点击界面元素
  参数：无
2.long_press
  作用：模拟用户长按行为
  参数：无
3.drag
  作用：模拟抓取拖拽操作，将元素从a点拖拽到b点
  参数：无
4.scroll(direction='up/down/left/right')
  作用：滑动手机屏幕
  参数：direction='up/down/left/right'
  操作说明：
    - direction='up': 屏幕向上移动
    - direction='down': 屏幕向下移动
    - direction='left': 屏幕向左移动
    - direction='right': 屏幕向右移动
5.type(content='输入的内容')
  作用：模拟输入内容
  参数：content='输入的内容'
  操作说明：
    - 输入内容前，必须先使用click点击输入框，必须呼出输入法, 当前界面底部出现输入法，才能使用type输入内容
    - 需要删除原有内容，请先调用delete
6.delete(content=删除文字数量)
  作用：模拟删除输入框内容，content参数指定删除的字符个数
  参数：content=删除文字数量
  操作说明：
    - 删除内容前，必须先使用click点击输入框，必须呼出输入法, 当前界面必须看到输入法，才能使用delete删除内容
7.back
  作用：模拟 Android 返回键
  参数：无
  操作说明：
    - 用于返回上一级页面，或关闭弹窗
8.wait(seconds=等待秒数)
  作用：等待一段时间
  参数：seconds=等待秒数，单位秒，默认值为5秒
  操作说明：
    - 用于页面加载、动画、广告、弹窗自动消失。
9.enter
  作用：模拟回车键
  参数：无
  操作说明：
    - 用于搜索框输入内容后直接搜索，当找不到搜索按钮时使用。
10.finished(content='执行结果')
  描述：测试用例执行完成
  参数：content,测试用例执行结果
  操作说明：
    - 整个测试用例步骤执行完毕，达成<测试用例信息>期望结果，调用finished，并填写content说明结果
11.failed(content='失败原因 ')
  作用：测试用例步骤执行失败
  参数：content失败原因  
  操作说明：
    - 自检未通过，主动失败退出，并填写content说明失败原因"""


def get_ui_component_instructions(state: DeploymentState) -> str:
    """获取UI组件操作说明"""
    custom_instructions = state.get("ui_component_instructions")
    if custom_instructions:
        return custom_instructions
    return """1.**日期选择器**
 - **组件结构：** 
  1.日期选择器分为三行，中间行为当前日期，通过点击选中中间行年月日来修改日期，禁止使用scroll滑动修改日期
  2.只允许点击中间一行（年/月/日），禁止点击上下行日期。
    - 正确示例：要修改'1995'，则只能点击'1995'，让'1995'处于选中状态，被蓝色背景覆盖
    - 错误示例：要修改'1995'，点击上下行的'1994'或'1996'，'1995'未被选中
 - **操作方法：**
  1.使用click点击中间一行要修改的日期，选中日期，日期数字被选中，必须呼出输入法
  2.调用delete删除被选中日期，必须必须呼出输入法才能删除
  3.调用type动作输入新日期数字，界面底部必须呼出输入法，并且已经删除旧日期

2.**输入框** 
 - **操作方法：**
  1.输入框输入内容前，必须先使用click点击输入框，必须呼出输入法, 当前界面底部必须看到输入法
  2.如果输入框很大很宽，必须点击输入框第一行位置呼出输入法
  3.如果需要修改内容，需要调用delete删除原有内容

3.**按钮操作规范**
 - **操作方法：**
  1.点击前要区分是否可被点击，灰色按钮一般为不可点击，蓝色或其他颜色一般为可点击
  2.多次点击按钮没反应则说明按钮当前不可点击，重新思考，需要先满足点击条件

4.**导航栏/标签栏操作规范**
 - **组件结构：** 
  1.导航栏/标签栏包含多个标签，例如分类标签等
 - **操作方法：**  
  1.选择导航栏/标签栏，每次都选择滑动方向的第二个标签作为滑动起点，向左或向右滑动
  2.滑动寻找标签时，要先朝着一个方向滑动，直到找到目标标签为止
  3.必须在输出指令字段描述作为滑动起点标签的'内容'
"""


def get_special_scenarios(state: DeploymentState) -> str:
    """获取特殊场景处理说明"""
    custom_scenarios = state.get("special_scenarios")
    if custom_scenarios:
        return custom_scenarios

    return """1.如果界面出现弹窗，且弹窗带倒计时，则调用wait(seconds=倒计时秒数)等待倒计时结束自动消失
2.如果界面出现弹窗，且弹窗不带倒计时，但附近存在'我知道了'、'同意'、'取消' 'X'等按钮，则调用click点击按钮关闭弹窗
3.如果上面两个条件都不满足，则调用back返回上一级"""


def get_image_element_extractor_prompt() -> str:
    return """1.仔细阅读分析图片，拆解图片中元素，定位与'当前测试用例步骤'描述相关的元素
  - 记录所有被定位的元素特征(文字、形状等)、元素绝对位置、相对于相邻元素的相对位置，保存到你的记忆中"""


def get_interface_analysis_prompt() -> str:
    """获取界面分析提示"""
   #  return """1. 结合 <执行记忆界面截图> 、<当前轮界面截图> 和 <测试用例信息> 的'用例执行步骤'理解界面中图标、UI组件、文字内容和各元素的特征和位置信息
   # - 在界面中定位与'用例步骤'的元素，必须遵循'用例步骤'描述的**元素特征**和**位置信息**
   # - 禁止伪造、猜测不存在的元素和内容。"""
    return """1. 结合 <当前轮界面截图> 和 <测试用例信息> 的'用例执行步骤'理解界面中图标、UI组件、文字内容和各元素的特征和位置信息
   - 在界面中定位与'用例步骤'的元素，必须遵循'用例步骤'描述的**元素特征**和**位置信息**
   - 禁止伪造、猜测不存在的元素和内容。"""


def get_red_mark_prompt() -> str:
    return """1. 准星标记，用于定位已经被点击的元素
2. 准星标记外层一个红圈，中心有一个红色实心圆点"""


def get_rollback_guidance() -> str:
    return """1.查看<执行记忆>中上一轮执行动作，按照以下方式进行回滚
  - 当上一轮动作是click、long_press：调用back返回,或点击返回图标，或点击关闭图标进行返回，或根据界面实际情况进行返回
  - 当上一轮动作是scroll、drag：继续滑动寻找元素即可"""


def get_self_check_prompt() -> str:
    """获取自检流程提示"""
    return """请严格按照以下规则进行自检判断，并返回明确结论。每一项检测必须完成，不允许省略或模糊判断。
1. **动作执行验证**
   - 读取<动作执行验证>，如果存在失败原因，说明上一轮动作执行失败，按照<回滚动作>说明进行回滚

2. **用例路径偏离检测**
     - 结合<执行记忆>和<测试用例信息>分析，发现执行5次脱离了用例的执行路径，导致无法按原定用例步骤继续推进，则调用 Failed 动作结束流程

3. **步骤完成检测**
   * 检测目的
     - 为了检测上一步骤是否已经完成，可以切换到下一个步骤
   * 判定规则:
       1.如果<步骤预期结果验证>存在'验证错误步骤'和'验证错误原因'
         - 情况1：<当前轮界面截图> 与 <执行记忆界面截图>上一轮截图元素一致时，则，重新定位上一轮执行步骤中的元素，重写决策内容
         - 情况2: <当前轮界面截图> 与 <执行记忆界面截图>上一轮截图元素发生变化，则调用<回滚动作>进行回滚,**读取<执行记忆>，重新定位上一轮执行步骤中的元素，重写决策内容** 
         - 其他情况：自行决策
       2.如果在<当前轮界面截图>无法找到<测试用例信息>下一步骤需要操作的元素，则上一步骤执行错误，立即调用<回滚动作>进行回滚，**读取<执行记忆>，重新定位上一轮执行步骤中的元素，重写决策内容**  

4. **重复操作检测**
     - 分析<执行记忆>内容，如果最近5轮重试，界面未产生变化效果，则调用 Failed 动作，结束流程"""


def get_action_decision_prompt() -> str:
# 2. 获取<执行记忆>最近3轮执行决策、执行动作和执行步骤，并结合'记忆中的相关元素'，确定下一个执行动作和目标元素；
    return f"""1. 通过<图片元素提取>分析<当前轮界面截图>，将当前步骤操作的相关元素，提取并保存到你的记忆中；
2. 获取<执行记忆>最近3轮执行决策、执行动作和执行步骤，并结合'记忆中的相关元素'，根据<测试用例信息>的'用例步骤'确定下一个执行动作和目标元素；
3. 优先考虑 <特殊场景>，按照场景说明选择指定动作；
4. 结合 <动作列表> 根据 <UI组件操作说明> 选择指定动作，如果组件不存在 <UI组件操作说明> 则使用先验知识选择指定动作；
5. 整个测试用例步骤执行完毕，达成<测试用例信息>期望结果，调用finished，并填写content说明结果；"""


def get_execution_step_rule_prompt() -> str:
    return """1. 要按<测试用例信息> 中步骤顺序执行，不允许跳过用例步骤，每个用例步骤可能存在多个执行动作；
2. 动作操作的界面元素必须与用例步骤描述的内容完全一致，不要根据 <当前轮界面截图> 猜想元素的内容;"""


def get_output_example() -> str:
    """获取输出格式要求"""
    return """{{
  "self_check": "输出<自检流程>的自检结果",
  "interface_analysis": "当前界面(继续补充内容,保持语言简洁)",
  "current_step_name": "当前正在执行的步骤名称，从<测试用例信息>获取，按照步骤顺序一个个执行",
  "action_decision": "描述动作类型；从<动作列表>中获取对应动作的'操作说明'，根据'操作说明'填充决策内容(保持语言简洁)",
  "instruction": "参考动作决策，仅提取UI操作相关内容，禁止描述任何操作目的、意图、期望结果、判断条件以及动作参数
   - click：必须描述被点击的具体元素特征(文字、形状等);先描述元素绝对位置，再描述元素相对于相邻元素的相对位置
   - long_press：必须描述被长按的具体元素特征(文字、形状等);先描述元素绝对位置，再描述元素相对于相邻元素的相对位置
   - drag：必须描述被拖拽的具体元素特征(文字、形状等)；描述目标位置元素特征(文字、形状等)
   - scroll：必须描述滑动起点的具体元素特征(文字、形状等);先描述元素绝对位置，再描述元素相对于相邻元素的相对位置
  "
  "action": "动作+参数，必须参照<动作列表>"
}}"""


# 请仅输出 UI 操作动作本身（动作 + 起始元素 + 参数），禁止描述任何目的、意图、期望结果或判断条件

def get_output_requirement() -> str:
    return """
1.使用JSON格式输出内容，严格遵循<输出格式>，保证输出内容与格式完全一致
"""


def get_screenshot_description(current_execution_count, has_execution_history):
    if has_execution_history:
        screenshot_context = f"""当前是第{current_execution_count}轮执行，你能看到最近2轮的<执行记忆界面截图>和<当前轮界面截图>
 1. 执行记忆界面截图：显示最近2轮执行的界面截图，被点击的位置会有一个<准星标记>
 2. 当前轮界面截图：手机界面最新截图

**重要提示**：
 - <执行记忆界面截图>中如果存在<准星标记>，说明是该轮动作操作的具体位置
 - <准星标记>帮助你定位历史操作元素"""
    else:
        screenshot_context = f"""当前是第{current_execution_count}轮执行，你只能看到<当前轮界面截图>"""
    return screenshot_context


def get_execute_history(execution_records) -> str:
    """获取执行历史"""

    if not execution_records:
        return ""

    history_content = ""
    for record in execution_records:
        decision_content = record.get("decision_content", "")
        record_execution_count = record.get("execution_count", "")
        step_verification_failure = record.get("step_verification_failure")
        record_status = record.get("status", "")

        # 显示执行记录（包括被阻塞的记录，但不显示决策内容）
        if decision_content and record_status != "blocked":
            history_content += f"""
**第{record_execution_count}轮执行**
 {_escape_template_variables(decision_content)}"""

        # 紧跟着显示步骤验证失败信息（如果存在）
        if step_verification_failure:
            failure_info = f""" *验证失败*
 *失败原因*：{step_verification_failure['failure_reason']}"""
            history_content += failure_info
    return history_content


def get_action_verification_failure_context(state: DeploymentState) -> str:
    action_verification_failure_reason = state.get("action_verification_failure_reason")
    if action_verification_failure_reason:
        context = f"""
- 失败原因：{action_verification_failure_reason}
"""
        return context
    return ""


def get_step_verification_failure_context(state: DeploymentState) -> str:
    verification_failure_reason = state.get("verification_failure_reason")
    if verification_failure_reason:
        # 从历史记录中获取验证失败的步骤信息
        history = state.get("history", [])

        # 查找最近的验证失败记录
        verification_failure_info = None
        for record in reversed(history):
            if record.get("step_verification_failure"):
                verification_failure_info = record["step_verification_failure"]
                break

        if verification_failure_info:
            step_index = verification_failure_info.get("step_index", 0)
            step_name = verification_failure_info.get("step_name", "")
            context = f"""- 验证失败步骤：第{step_index + 1}步 - "{step_name}"
- 验证失败原因：{verification_failure_reason}"""
        else:
            # 如果没有找到验证失败信息，使用当前步骤信息作为备选
            current_step_index = state.get("current_step_index", 0)
            task_steps = state.get("task_steps", [])
            current_step_name = task_steps[current_step_index] if current_step_index < len(
                task_steps) else f"步骤{current_step_index + 1}"
            context = f"""- 验证失败步骤：第{current_step_index + 1}步 - "{current_step_name}"
- 验证失败原因：{verification_failure_reason}"""

        # 立即清空verification_failure_reason，确保只显示一轮
        state["verification_failure_reason"] = None

        return context

    return ""


def _escape_template_variables(text: str) -> str:
    """
        转义文本中的模板变量符号，防止LangChain将其识别为变量

        Args:
            text: 原始文本

        Returns:
            转义后的文本
        """
    if not text:
        return text

    # 将单个花括号转义为双花括号
    # 这样LangChain就不会将其识别为模板变量
    return text.replace("{", "{{").replace("}", "}}")
