"""Initial migration

Revision ID: ff7e4a6f5c4b
Revises: ed4ace0ea394
Create Date: 2025-07-21 14:27:37.756018

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'ff7e4a6f5c4b'
down_revision: Union[str, Sequence[str], None] = 'ed4ace0ea394'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('ui_task_action', 'execution_log')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('ui_task_action', sa.Column('execution_log', mysql.MEDIUMTEXT(collation='utf8mb4_general_ci'), nullable=True, comment='详细执行日志'))
    # ### end Alembic commands ###
