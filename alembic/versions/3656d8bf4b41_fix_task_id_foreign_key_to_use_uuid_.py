"""Fix task_id foreign key to use UUID instead of auto-increment ID

Revision ID: 3656d8bf4b41
Revises: 5783b52d1377
Create Date: 2025-07-18 15:25:47.555843

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '3656d8bf4b41'
down_revision: Union[str, Sequence[str], None] = '5783b52d1377'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    # 先删除外键约束
    op.drop_constraint('ui_task_action_ibfk_1', 'ui_task_action', type_='foreignkey')
    # 修改字段类型
    op.alter_column('ui_task_action', 'task_id',
               existing_type=mysql.BIGINT(unsigned=True),
               type_=sa.String(length=64),
               comment='关联的任务UUID',
               existing_comment='关联的任务主键ID',
               existing_nullable=False)
    # 重新创建外键约束，关联到ui_task.task_id
    op.create_foreign_key('ui_task_action_task_id_fk', 'ui_task_action', 'ui_task', ['task_id'], ['task_id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'ui_task_action', type_='foreignkey')
    op.create_foreign_key(op.f('ui_task_action_ibfk_1'), 'ui_task_action', 'ui_task', ['task_id'], ['id'])
    op.alter_column('ui_task_action', 'task_id',
               existing_type=sa.String(length=64),
               type_=mysql.BIGINT(unsigned=True),
               comment='关联的任务主键ID',
               existing_comment='关联的任务UUID',
               existing_nullable=False)
    # ### end Alembic commands ###
