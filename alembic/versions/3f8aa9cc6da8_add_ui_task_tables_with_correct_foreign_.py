"""add ui task tables with correct foreign key types

Revision ID: 3f8aa9cc6da8
Revises: 34db2eeafb04
Create Date: 2025-07-18 11:49:52.475381

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '3f8aa9cc6da8'
down_revision: Union[str, Sequence[str], None] = '34db2eeafb04'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('ui_task_action',
    sa.Column('task_id', mysql.BIGINT(unsigned=True), nullable=False, comment='关联的任务主键ID'),
    sa.Column('step_name', sa.String(length=255), nullable=False, comment='执行的步骤名称'),
    sa.Column('execution_time', sa.Float(), nullable=True, comment='执行耗时(秒)'),
    sa.Column('start_time', sa.DateTime(), nullable=True, comment='开始时间'),
    sa.Column('end_time', sa.DateTime(), nullable=True, comment='结束时间'),
    sa.Column('status', sa.String(length=32), nullable=True, comment='执行状态: running/success/failed'),
    sa.Column('error_message', sa.Text(), nullable=True, comment='错误信息'),
    sa.Column('decision_content', mysql.MEDIUMTEXT(), nullable=True, comment='决策Agent的分析内容'),
    sa.Column('action', sa.Enum('CLICK', 'LONG_PRESS', 'TYPE', 'DELETE', 'INPUT', 'SWIPE', 'DRAG', 'SCROLL', 'BACK', 'WAIT', 'ENTER', 'FINISHED', name='actiontype'), nullable=True, comment='动作类型'),
    sa.Column('after_screenshot', mysql.MEDIUMTEXT(), nullable=True, comment='执行后截图(base64)'),
    sa.Column('screenshot_path', sa.String(length=512), nullable=True, comment='截图文件路径'),
    sa.Column('verification_result', sa.JSON(), nullable=True, comment='步骤验证结果'),
    sa.Column('expect_result', sa.JSON(), nullable=True, comment='步骤期望结果(JSON格式,包含text和image字段)'),
    sa.Column('expected_result', sa.Text(), nullable=True, comment='期望结果(兼容字段)'),
    sa.Column('execution_log', mysql.MEDIUMTEXT(), nullable=True, comment='详细执行日志'),
    sa.Column('id', mysql.BIGINT(unsigned=True), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False, comment='创建日期'),
    sa.Column('updated_at', sa.DateTime(), nullable=False, comment='更新日期'),
    sa.ForeignKeyConstraint(['task_id'], ['ui_task.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_ui_task_action_task_id'), 'ui_task_action', ['task_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_ui_task_action_task_id'), table_name='ui_task_action')
    op.drop_table('ui_task_action')
    # ### end Alembic commands ###
