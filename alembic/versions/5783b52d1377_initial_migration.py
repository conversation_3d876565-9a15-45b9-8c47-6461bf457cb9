"""Initial migration

Revision ID: 5783b52d1377
Revises: 3f8aa9cc6da8
Create Date: 2025-07-18 15:20:32.138767

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '5783b52d1377'
down_revision: Union[str, Sequence[str], None] = '3f8aa9cc6da8'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('ui_task', 'status',
               existing_type=mysql.ENUM('READY', 'RUNNING', 'COMPLETED', 'FAILED', 'ERROR', 'PAUSED', collation='utf8mb4_general_ci'),
               comment='任务状态: ready/running/completed/failed/error/paused',
               existing_comment='任务状态: running/completed/failed',
               existing_nullable=False)
    op.alter_column('ui_task_action', 'action',
               existing_type=mysql.ENUM('CLICK', 'LONG_PRESS', 'TYPE', 'DELETE', 'INPUT', 'SWIPE', 'DRAG', 'SCROLL', 'BACK', 'WAIT', 'ENTER', 'FINISHED', collation='utf8mb4_general_ci'),
               type_=sa.String(length=512),
               comment='动作命令(完整的动作命令，包含坐标)',
               existing_comment='动作类型',
               existing_nullable=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('ui_task_action', 'action',
               existing_type=sa.String(length=512),
               type_=mysql.ENUM('CLICK', 'LONG_PRESS', 'TYPE', 'DELETE', 'INPUT', 'SWIPE', 'DRAG', 'SCROLL', 'BACK', 'WAIT', 'ENTER', 'FINISHED', collation='utf8mb4_general_ci'),
               comment='动作类型',
               existing_comment='动作命令(完整的动作命令，包含坐标)',
               existing_nullable=True)
    op.alter_column('ui_task', 'status',
               existing_type=mysql.ENUM('READY', 'RUNNING', 'COMPLETED', 'FAILED', 'ERROR', 'PAUSED', collation='utf8mb4_general_ci'),
               comment='任务状态: running/completed/failed',
               existing_comment='任务状态: ready/running/completed/failed/error/paused',
               existing_nullable=False)
    # ### end Alembic commands ###
