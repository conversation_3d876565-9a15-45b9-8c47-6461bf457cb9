"""Initial migration

Revision ID: f0a19daa42b9
Revises: ff7e4a6f5c4b
Create Date: 2025-07-21 14:34:16.903339

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'f0a19daa42b9'
down_revision: Union[str, Sequence[str], None] = 'ff7e4a6f5c4b'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('ui_task_action', 'execution_time')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('ui_task_action', sa.Column('execution_time', mysql.FLOAT(), nullable=True, comment='执行耗时(秒)'))
    # ### end Alembic commands ###
