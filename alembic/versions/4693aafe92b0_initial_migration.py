"""Initial migration

Revision ID: 4693aafe92b0
Revises: 4ddbe49a48c0
Create Date: 2025-07-21 16:50:21.677237

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '4693aafe92b0'
down_revision: Union[str, Sequence[str], None] = '4ddbe49a48c0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('ui_task_action', 'status',
               existing_type=mysql.VARCHAR(collation='utf8mb4_general_ci', length=32),
               comment='执行状态: processing/succeed/failed',
               existing_comment='执行状态: running/success/failed',
               existing_nullable=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('ui_task_action', 'status',
               existing_type=mysql.VARCHAR(collation='utf8mb4_general_ci', length=32),
               comment='执行状态: running/success/failed',
               existing_comment='执行状态: processing/succeed/failed',
               existing_nullable=True)
    # ### end Alembic commands ###
