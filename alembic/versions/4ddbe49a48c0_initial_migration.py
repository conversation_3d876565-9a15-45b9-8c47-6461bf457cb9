"""Initial migration

Revision ID: 4ddbe49a48c0
Revises: f0a19daa42b9
Create Date: 2025-07-21 16:20:55.144486

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '4ddbe49a48c0'
down_revision: Union[str, Sequence[str], None] = 'f0a19daa42b9'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('ui_task', 'execution_count')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('ui_task', sa.Column('execution_count', mysql.INTEGER(), autoincrement=False, nullable=True, comment='执行轮数'))
    # ### end Alembic commands ###
