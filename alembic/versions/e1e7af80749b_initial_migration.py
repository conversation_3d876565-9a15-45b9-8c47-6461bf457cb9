"""Initial migration

Revision ID: e1e7af80749b
Revises: 4693aafe92b0
Create Date: 2025-07-22 13:51:47.067947

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'e1e7af80749b'
down_revision: Union[str, Sequence[str], None] = '4693aafe92b0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('ui_task_action', sa.Column('image_path', sa.String(length=512), nullable=True, comment='执行后截图地址'))
    op.drop_column('ui_task_action', 'screenshot')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('ui_task_action', sa.Column('screenshot', mysql.MEDIUMBLOB(), nullable=True, comment='执行后截图(base64)'))
    op.drop_column('ui_task_action', 'image_path')
    # ### end Alembic commands ###
