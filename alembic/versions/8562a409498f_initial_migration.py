"""Initial migration

Revision ID: 8562a409498f
Revises: 6b21da5f620a
Create Date: 2025-07-21 13:52:57.144206

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '8562a409498f'
down_revision: Union[str, Sequence[str], None] = '6b21da5f620a'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('ui_task', 'status',
               existing_type=mysql.ENUM('READY', 'RUNNING', 'COMPLETED', 'FAILED', 'ERROR', 'PAUSED', charset='utf8mb4', collation='utf8mb4_general_ci'),
               comment='任务状态: processing/succeed/failed',
               existing_comment='任务状态: ready/running/completed/failed/error/paused',
               existing_nullable=False)
    op.drop_column('ui_task', 'expected_result_text')
    op.drop_column('ui_task', 'device_config')
    op.drop_column('ui_task', 'execution_duration')
    op.drop_column('ui_task', 'expected_result_image')
    op.drop_column('ui_task_action', 'expected_result')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('ui_task_action', sa.Column('expected_result', mysql.TEXT(collation='utf8mb4_general_ci'), nullable=True, comment='期望结果(兼容字段)'))
    op.add_column('ui_task', sa.Column('expected_result_image', mysql.MEDIUMBLOB(), nullable=True, comment='期望结果图片(base64)(兼容字段)'))
    op.add_column('ui_task', sa.Column('execution_duration', mysql.FLOAT(), nullable=True, comment='执行时长(秒)'))
    op.add_column('ui_task', sa.Column('device_config', mysql.JSON(), nullable=True, comment='设备配置信息(兼容字段)'))
    op.add_column('ui_task', sa.Column('expected_result_text', mysql.TEXT(collation='utf8mb4_general_ci'), nullable=True, comment='期望结果文本描述(兼容字段)'))
    op.alter_column('ui_task', 'status',
               existing_type=mysql.ENUM('READY', 'RUNNING', 'COMPLETED', 'FAILED', 'ERROR', 'PAUSED', charset='utf8mb4', collation='utf8mb4_general_ci'),
               comment='任务状态: ready/running/completed/failed/error/paused',
               existing_comment='任务状态: processing/succeed/failed',
               existing_nullable=False)
    # ### end Alembic commands ###
