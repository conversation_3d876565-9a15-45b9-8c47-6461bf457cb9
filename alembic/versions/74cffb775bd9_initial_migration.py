"""Initial migration

Revision ID: 74cffb775bd9
Revises: 8562a409498f
Create Date: 2025-07-21 14:15:02.175977

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '74cffb775bd9'
down_revision: Union[str, Sequence[str], None] = '8562a409498f'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('ui_task', 'device')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('ui_task', sa.Column('device', mysql.JSON(), nullable=True, comment='设备配置信息(完整JSON结构)'))
    # ### end Alembic commands ###
