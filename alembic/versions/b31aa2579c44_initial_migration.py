"""Initial migration

Revision ID: b31aa2579c44
Revises: e1e7af80749b
Create Date: 2025-07-24 14:08:51.802048

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'b31aa2579c44'
down_revision: Union[str, Sequence[str], None] = 'e1e7af80749b'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('ui_task', sa.Column('image_path', sa.String(length=512), nullable=True, comment='执行后截图地址'))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('ui_task', 'image_path')
    # ### end Alembic commands ###
