"""Change image fields from MEDIUMTEXT to MEDIUMBLOB

Revision ID: 6b21da5f620a
Revises: 3656d8bf4b41
Create Date: 2025-07-21 12:09:17.188855

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '6b21da5f620a'
down_revision: Union[str, Sequence[str], None] = '3656d8bf4b41'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    # 将图片字段从MEDIUMTEXT改为MEDIUMBLOB以优化存储
    op.alter_column('ui_task', 'expected_result_image',
               existing_type=mysql.MEDIUMTEXT(collation='utf8mb4_general_ci'),
               type_=mysql.MEDIUMBLOB(),
               existing_comment='期望结果图片(base64)(兼容字段)',
               existing_nullable=True)

    # 将截图字段从MEDIUMTEXT改为MEDIUMBLOB，并重命名字段
    op.add_column('ui_task_action', sa.Column('screenshot', mysql.MEDIUMBLOB(), nullable=True, comment='执行后截图(base64)'))

    # 数据迁移：将after_screenshot的base64数据转换为二进制存储到screenshot字段
    # 注意：这里需要将base64字符串解码为二进制数据
    op.execute("""
        UPDATE ui_task_action
        SET screenshot = FROM_BASE64(after_screenshot)
        WHERE after_screenshot IS NOT NULL AND after_screenshot != ''
    """)

    # 删除旧字段
    op.drop_column('ui_task_action', 'after_screenshot')
    op.drop_column('ui_task_action', 'screenshot_path')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    # 恢复旧字段
    op.add_column('ui_task_action', sa.Column('after_screenshot', mysql.MEDIUMTEXT(collation='utf8mb4_general_ci'), nullable=True, comment='执行后截图(base64)'))
    op.add_column('ui_task_action', sa.Column('screenshot_path', mysql.VARCHAR(collation='utf8mb4_general_ci', length=512), nullable=True, comment='截图文件路径'))

    # 数据迁移：将二进制数据转换回base64字符串
    op.execute("""
        UPDATE ui_task_action
        SET after_screenshot = TO_BASE64(screenshot)
        WHERE screenshot IS NOT NULL
    """)

    # 删除新字段
    op.drop_column('ui_task_action', 'screenshot')

    # 恢复ui_task表的字段类型
    op.alter_column('ui_task', 'expected_result_image',
               existing_type=mysql.MEDIUMBLOB(),
               type_=mysql.MEDIUMTEXT(collation='utf8mb4_general_ci'),
               existing_comment='期望结果图片(base64)(兼容字段)',
               existing_nullable=True)
    # ### end Alembic commands ###
